"""
一周热门话题发现系统 - Python版本
基于话题聚类和去重算法，确保话题多样性
"""

import requests
import json
import re
import time
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Any, Tuple, Set
import hashlib
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeeklyHotTopicsDetector:
    """一周热门话题发现器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://newsapi.org/v2"
        self.top_sources = [
            'bbc-news', 'cnn', 'reuters', 'associated-press', 
            'the-washington-post', 'bloomberg', 'techcrunch',
            'the-guardian-uk', 'abc-news', 'financial-times'
        ]
        
        # 停用词列表
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'over', 'under', 'again', 'further',
            'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
            'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very',
            'can', 'will', 'just', 'should', 'now', 'says', 'said', 'new', 'news'
        }
        
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'HotTopicsDetector/1.0'})

    def get_weekly_hot_topics(self) -> Dict[str, Any]:
        """获取一周内最热门的不重复话题"""
        now = datetime.now()
        one_week_ago = now - timedelta(days=7)
        
        logger.info("🔍 开始收集一周内的新闻数据...")
        
        try:
            # 多策略收集新闻
            news_collection = self.collect_weekly_news(one_week_ago, now)
            logger.info(f"📊 收集到 {len(news_collection)} 篇文章")
            
            # 话题聚类和提取
            topics = self.extract_and_cluster_topics(news_collection)
            logger.info(f"🏷️ 识别出 {len(topics)} 个话题")
            
            # 话题热度评分
            scored_topics = self.score_topics(topics)
            
            # 去重和多样性保证
            diverse_topics = self.ensure_topic_diversity(scored_topics)
            
            # 最终排序
            final_topics = sorted(diverse_topics, key=lambda x: x['heat_score'], reverse=True)[:20]

            return {
                'timestamp': now.isoformat(),
                'week_range': {
                    'from': one_week_ago.strftime('%Y-%m-%d'),
                    'to': now.strftime('%Y-%m-%d')
                },
                'total_articles_analyzed': len(news_collection),
                'unique_topics_found': len(final_topics),
                'hot_topics': final_topics
            }

        except Exception as error:
            logger.error(f"话题检测失败: {error}")
            return self.get_fallback_topics()

    def collect_weekly_news(self, from_date: datetime, to_date: datetime) -> List[Dict[str, Any]]:
        """多策略收集一周新闻"""
        from_str = from_date.strftime('%Y-%m-%d')
        to_str = to_date.strftime('%Y-%m-%d')
        
        all_news = []
        
        # 使用线程池并发获取新闻
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                # 策略1: 按热门度获取全量新闻
                executor.submit(self.get_news_with_params, {
                    'from': from_str,
                    'to': to_str,
                    'sortBy': 'popularity',
                    'pageSize': 100
                }),
                
                # 策略2: 各类别头条新闻
                executor.submit(self.get_category_news, 
                    ['business', 'technology', 'health', 'science', 'sports', 'entertainment']),
                
                # 策略3: 权威媒体新闻
                executor.submit(self.get_authority_media_news, from_str, to_str),
                
                # 策略4: 热门关键词新闻
                executor.submit(self.get_trending_keyword_news, from_str, to_str)
            ]
            
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if isinstance(result, list):
                        all_news.extend(result)
                    elif isinstance(result, dict) and 'articles' in result:
                        all_news.extend(result['articles'])
                except Exception as e:
                    logger.warning(f"获取新闻时出错: {e}")
        
        # 过滤和去重
        valid_news = [article for article in all_news 
                     if article and article.get('title') and article.get('publishedAt')]
        
        unique_news = self.deduplicate_by_url(valid_news)
        return unique_news

    def extract_and_cluster_topics(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """话题提取和聚类"""
        logger.info("🧠 开始话题聚类分析...")
        
        # 1. 提取每篇文章的关键词
        articles_with_keywords = []
        for article in articles:
            text = f"{article.get('title', '')} {article.get('description', '')}"
            keywords = self.extract_keywords(text)
            normalized_title = self.normalize_title(article.get('title', ''))
            
            articles_with_keywords.append({
                **article,
                'keywords': keywords,
                'normalized_title': normalized_title
            })

        # 2. 基于关键词相似度进行聚类
        clusters = self.perform_topic_clustering(articles_with_keywords)
        
        # 3. 为每个聚类生成话题描述
        topics = []
        for cluster in clusters:
            if len(cluster) >= 2:  # 至少2篇文章才算话题
                topic = {
                    'topic_id': self.generate_topic_id(cluster),
                    'topic_title': self.generate_topic_title(cluster),
                    'topic_keywords': self.extract_topic_keywords(cluster),
                    'articles_count': len(cluster),
                    'articles': cluster,
                    'date_range': self.get_cluster_date_range(cluster),
                    'sources': self.get_cluster_sources(cluster),
                    'categories': self.infer_topic_categories(cluster)
                }
                topics.append(topic)

        return topics

    def extract_keywords(self, text: str) -> Dict[str, int]:
        """关键词提取"""
        # 清理和分词
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = [word for word in text.split() 
                if len(word) > 3 and word not in self.stop_words]
        
        # 统计词频
        return dict(Counter(words))

    def normalize_title(self, title: str) -> str:
        """标题标准化"""
        return re.sub(r'\s+', ' ', re.sub(r'[^\w\s]', ' ', title.lower())).strip()

    def perform_topic_clustering(self, articles: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """话题聚类算法"""
        clusters = []
        processed = set()

        for i, article in enumerate(articles):
            if i in processed:
                continue

            cluster = [article]
            processed.add(i)

            # 寻找相似文章
            for j, other_article in enumerate(articles):
                if j == i or j in processed:
                    continue

                similarity = self.calculate_topic_similarity(article, other_article)
                
                if similarity > 0.4:  # 相似度阈值
                    cluster.append(other_article)
                    processed.add(j)

            if len(cluster) >= 2:
                clusters.append(cluster)

        return sorted(clusters, key=len, reverse=True)

    def calculate_topic_similarity(self, article1: Dict[str, Any], article2: Dict[str, Any]) -> float:
        """话题相似度计算"""
        # 1. 标题相似度
        title_sim = self.calculate_text_similarity(
            article1['normalized_title'],
            article2['normalized_title']
        )

        # 2. 关键词重叠度
        keywords1 = set(article1['keywords'].keys())
        keywords2 = set(article2['keywords'].keys())
        
        if not keywords1 or not keywords2:
            keyword_sim = 0
        else:
            intersection = keywords1 & keywords2
            union = keywords1 | keywords2
            keyword_sim = len(intersection) / len(union)

        # 3. 实体相似度（简化版本）
        entities1 = self.extract_simple_entities(article1.get('title', ''))
        entities2 = self.extract_simple_entities(article2.get('title', ''))
        entity_intersection = set(entities1) & set(entities2)
        entity_sim = 0.3 if entity_intersection else 0

        # 综合相似度
        return title_sim * 0.4 + keyword_sim * 0.4 + entity_sim * 0.2

    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """文本相似度计算（Jaccard相似度）"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0
        
        intersection = words1 & words2
        union = words1 | words2
        
        return len(intersection) / len(union)

    def extract_simple_entities(self, text: str) -> List[str]:
        """简单实体提取（大写字母开头的词组）"""
        pattern = r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        return re.findall(pattern, text)

    def generate_topic_title(self, cluster: List[Dict[str, Any]]) -> str:
        """话题标题生成"""
        # 找到最频繁的关键词组合
        all_keywords = defaultdict(int)
        
        for article in cluster:
            for keyword, count in article['keywords'].items():
                all_keywords[keyword] += count

        # 获取最热门的关键词
        top_keywords = [keyword for keyword, _ in 
                       sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)[:3]]

        # 尝试从标题中提取最有代表性的短语
        representative_title = self.find_most_representative_title(cluster)
        
        return representative_title or ' '.join(top_keywords)

    def find_most_representative_title(self, cluster: List[Dict[str, Any]]) -> str:
        """寻找最有代表性的标题"""
        if not cluster:
            return ""
        
        # 获取所有标题的词汇
        title_words = []
        for article in cluster:
            words = [w for w in article.get('title', '').lower().split() if len(w) > 3]
            title_words.append(set(words))

        if not title_words:
            return cluster[0].get('title', '')[:50]

        # 找出所有标题中共同的词汇
        common_words = title_words[0]
        for words in title_words[1:]:
            common_words &= words

        if len(common_words) >= 2:
            return ' '.join(list(common_words)[:4])

        # 如果没有共同词汇，返回最短的标题
        shortest_title = min(cluster, key=lambda x: len(x.get('title', '')))
        title = shortest_title.get('title', '')
        return title.split(':', 1)[0].split('-', 1)[0].strip()

    def score_topics(self, topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """话题热度评分"""
        scored_topics = []
        
        for topic in topics:
            score = 0

            # 1. 文章数量权重 (30%)
            import math
            score += math.log(topic['articles_count']) * 3

            # 2. 媒体来源多样性 (25%)
            unique_sources = len(topic['sources'])
            score += unique_sources * 2.5

            # 3. 时间分布 (20%) - 持续热度vs突发热度
            time_span = self.calculate_time_span(topic['date_range'])
            time_score = min(time_span, 2) if time_span > 2 else time_span
            score += time_score * 2

            # 4. 权威媒体覆盖 (15%)
            authority_count = sum(1 for source in topic['sources'] 
                                if source.get('id') in self.top_sources)
            score += authority_count * 1.5

            # 5. 新鲜度 (10%)
            latest_date = max(datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00'))
                            for article in topic['articles'])
            hours_ago = (datetime.now() - latest_date.replace(tzinfo=None)).total_seconds() / 3600
            freshness_score = max(0, 3 - hours_ago / 24)  # 3天内有加分
            score += freshness_score

            scored_topic = {
                **topic,
                'heat_score': score,
                'scoring_details': {
                    'article_count_score': math.log(topic['articles_count']) * 3,
                    'source_diversity_score': unique_sources * 2.5,
                    'time_span_score': time_score * 2,
                    'authority_score': authority_count * 1.5,
                    'freshness_score': freshness_score
                }
            }
            scored_topics.append(scored_topic)

        return scored_topics

    def ensure_topic_diversity(self, topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """确保话题多样性"""
        diverse_topics = []
        used_keywords = set()

        # 按热度排序，然后选择关键词不重叠的话题
        sorted_topics = sorted(topics, key=lambda x: x['heat_score'], reverse=True)
        
        for topic in sorted_topics:
            topic_keywords = topic['topic_keywords'][:3]
            has_overlap = any(keyword in used_keywords for keyword in topic_keywords)

            if not has_overlap or len(diverse_topics) < 5:
                diverse_topics.append({
                    **topic,
                    'diversity_rank': len(diverse_topics) + 1
                })
                used_keywords.update(topic_keywords)

        return diverse_topics

    def extract_topic_keywords(self, cluster: List[Dict[str, Any]]) -> List[str]:
        """提取话题关键词"""
        all_keywords = defaultdict(int)
        
        for article in cluster:
            for keyword, count in article['keywords'].items():
                all_keywords[keyword] += count

        return [keyword for keyword, _ in 
                sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)[:5]]

    def get_cluster_date_range(self, cluster: List[Dict[str, Any]]) -> Dict[str, datetime]:
        """获取聚类的日期范围"""
        dates = []
        for article in cluster:
            try:
                date = datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00'))
                dates.append(date.replace(tzinfo=None))
            except:
                continue
        
        if not dates:
            now = datetime.now()
            return {'start': now, 'end': now}
        
        return {
            'start': min(dates),
            'end': max(dates)
        }

    def get_cluster_sources(self, cluster: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取聚类的媒体来源"""
        sources = {}
        for article in cluster:
            source = article.get('source')
            if source:
                source_id = source.get('id') or source.get('name', '')
                if source_id:
                    sources[source_id] = source
        
        return list(sources.values())

    def infer_topic_categories(self, cluster: List[Dict[str, Any]]) -> List[str]:
        """推断话题类别"""
        categories = set()
        
        # 基于来源推断类别
        for article in cluster:
            source_id = article.get('source', {}).get('id', '')
            if source_id in ['techcrunch', 'wired', 'ars-technica']:
                categories.add('technology')
            elif source_id in ['bloomberg', 'financial-times', 'reuters']:
                categories.add('business')
            elif source_id in ['bbc-sport', 'espn', 'sports-illustrated']:
                categories.add('sports')

        # 基于关键词推断
        all_text = ' '.join([
            f"{article.get('title', '')} {article.get('description', '')}"
            for article in cluster
        ]).lower()
        
        if re.search(r'\b(ai|artificial intelligence|tech|crypto|bitcoin)\b', all_text):
            categories.add('technology')
        if re.search(r'\b(economy|market|stock|finance|bank)\b', all_text):
            categories.add('business')
        if re.search(r'\b(health|medical|vaccine|covid|disease)\b', all_text):
            categories.add('health')
        if re.search(r'\b(election|government|president|politics)\b', all_text):
            categories.add('politics')

        return list(categories)

    def calculate_time_span(self, date_range: Dict[str, datetime]) -> float:
        """计算时间跨度（天数）"""
        return (date_range['end'] - date_range['start']).total_seconds() / (24 * 3600)

    def deduplicate_by_url(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据URL去重"""
        seen = set()
        unique_articles = []
        
        for article in articles:
            url = article.get('url', '')
            if url and url not in seen:
                seen.add(url)
                unique_articles.append(article)
        
        return unique_articles

    def generate_topic_id(self, cluster: List[Dict[str, Any]]) -> str:
        """生成话题ID"""
        if not cluster:
            return f"topic_{int(time.time())}"
        
        first_article = cluster[0]
        keywords = list(first_article.get('keywords', {}).keys())[:2]
        keywords_str = '_'.join(keywords) if keywords else 'unknown'
        return f"topic_{int(time.time())}_{keywords_str}".lower()

    # API调用方法
    def get_news_with_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """使用参数获取新闻"""
        url = f"{self.base_url}/everything"
        params['apiKey'] = self.api_key
        params['language'] = 'en'
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            return {'articles': []}

    def get_category_news(self, categories: List[str]) -> List[Dict[str, Any]]:
        """获取分类新闻"""
        all_articles = []
        
        for category in categories:
            try:
                url = f"{self.base_url}/top-headlines"
                params = {
                    'category': category,
                    'pageSize': 20,
                    'apiKey': self.api_key
                }
                
                response = self.session.get(url, params=params, timeout=30)
                if response.ok:
                    data = response.json()
                    all_articles.extend(data.get('articles', []))
                    
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                logger.warning(f"获取 {category} 类别新闻失败: {e}")
        
        return all_articles

    def get_authority_media_news(self, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """获取权威媒体新闻"""
        sources = ','.join(self.top_sources[:5])
        
        try:
            result = self.get_news_with_params({
                'sources': sources,
                'from': from_date,
                'to': to_date,
                'sortBy': 'publishedAt',
                'pageSize': 100
            })
            return result.get('articles', [])
        except Exception as e:
            logger.warning(f"获取权威媒体新闻失败: {e}")
            return []

    def get_trending_keyword_news(self, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """获取热门关键词新闻"""
        trending_keywords = [
            'trump', 'biden', 'ai', 'climate', 'economy', 
            'ukraine', 'china', 'covid', 'market', 'election'
        ]

        all_articles = []
        
        for keyword in trending_keywords[:5]:
            try:
                result = self.get_news_with_params({
                    'q': keyword,
                    'from': from_date,
                    'to': to_date,
                    'sortBy': 'popularity',
                    'pageSize': 20
                })
                all_articles.extend(result.get('articles', []))
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                logger.warning(f"获取关键词 {keyword} 新闻失败: {e}")

        return all_articles

    def get_fallback_topics(self) -> Dict[str, Any]:
        """备用方案"""
        return {
            'timestamp': datetime.now().isoformat(),
            'week_range': {'from': 'unknown', 'to': 'unknown'},
            'total_articles_analyzed': 0,
            'unique_topics_found': 0,
            'hot_topics': [],
            'error': 'Failed to fetch topics'
        }


# 使用示例和演示函数
def demonstrate_weekly_topics():
    """演示一周话题检测"""
    # 替换为你的API密钥
    api_key = "6975e8c0e3904a499b90c2e8002b6052"
    
    detector = WeeklyHotTopicsDetector(api_key)
    
    try:
        print("🚀 开始分析一周热门话题...\n")
        
        result = detector.get_weekly_hot_topics()
        
        print("📈 分析完成！")
        print(f"📅 分析时间范围: {result['week_range']['from']} 到 {result['week_range']['to']}")
        print(f"📊 分析文章总数: {result['total_articles_analyzed']}")
        print(f"🏷️ 识别话题数量: {result['unique_topics_found']}\n")
        
        print("🔥 一周最热门话题 (无重复):")
        print("=" * 50)
        
        for index, topic in enumerate(result['hot_topics'], 1):
            print(f"\n{index}. {topic['topic_title'].upper()}")
            print(f"   🔥 热度评分: {topic['heat_score']:.2f}")
            print(f"   📰 相关文章: {topic['articles_count']} 篇")
            print(f"   📡 媒体来源: {len(topic['sources'])} 个")
            print(f"   🏷️ 关键词: {', '.join(topic['topic_keywords'])}")
            
            date_range = topic['date_range']
            start_date = date_range['start'].strftime('%Y-%m-%d') if isinstance(date_range['start'], datetime) else str(date_range['start'])
            end_date = date_range['end'].strftime('%Y-%m-%d') if isinstance(date_range['end'], datetime) else str(date_range['end'])
            print(f"   📅 持续时间: {start_date} - {end_date}")
            
            if topic['categories']:
                print(f"   🗂️ 分类: {', '.join(topic['categories'])}")
            
            print(f"   📖 代表性文章:")
            for article in topic['articles'][:2]:
                print(f"      • {article.get('title', 'No title')}")
                source_name = article.get('source', {}).get('name', 'Unknown')
                pub_date = datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00')).strftime('%Y-%m-%d')
                print(f"        ({source_name} - {pub_date})")
        
        # 保存结果到文件
        with open('weekly_hot_topics.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n💾 结果已保存到 weekly_hot_topics.json")
        
    except Exception as error:
        print(f"❌ 分析失败: {error}")


def quick_demo():
    """快速演示"""
    api_key = "6975e8c0e3904a499b90c2e8002b6052"
    detector = WeeklyHotTopicsDetector(api_key)
    
    try:
        result = detector.get_weekly_hot_topics()
        
        print("🔥 一周最热门话题 TOP 5:")
        for i, topic in enumerate(result['hot_topics'][:5], 1):
            print(f"{i}. {topic['topic_title']}")
            print(f"   热度: {topic['heat_score']:.1f} | 文章: {topic['articles_count']}篇")
            print()
            
    except Exception as e:
        print(f"演示失败: {e}")


if __name__ == "__main__":
    # 运行完整演示
    demonstrate_weekly_topics()
    
    # 或者运行快速演示
    # quick_demo()