#!/usr/bin/env python3
import requests

def test_api():
    api_key = '6975e8c0e3904a499b90c2e8002b6052'
    url = 'https://newsapi.org/v2/top-headlines'
    params = {'country': 'us', 'pageSize': 1, 'apiKey': api_key}
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f'API状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'API调用成功，总文章数: {data.get("totalResults", 0)}')
            return True
        else:
            print(f'API调用失败: {response.text}')
            return False
    except Exception as e:
        print(f'请求异常: {e}')
        return False

if __name__ == "__main__":
    test_api() 